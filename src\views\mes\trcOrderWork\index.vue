<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="工单编号" prop="woNo">
          <el-input
            v-model="queryParams.woNo"
            placeholder="请输入工单编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            clearable
            v-model="queryParams.createTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="物料状态" prop="materialState">
          <el-input
            v-model="queryParams.materialState"
            placeholder="请输入物料状态"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <!-- <el-form-item label="产线id" prop="lineId">
        <el-input
          v-model="queryParams.lineId"
          placeholder="请输入产线id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="产线编码" prop="lineCode">
          <el-input
            v-model="queryParams.lineCode"
            placeholder="请输入产线编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产线名称" prop="lineName">
          <el-input
            v-model="queryParams.lineName"
            placeholder="请输入产线名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="排产数量" prop="productSchedule">
          <el-input
            v-model="queryParams.productSchedule"
            placeholder="请输入排产数量"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <!-- <el-form-item label="工单状态" prop="workorderStateArr">
          <el-select
            v-model="queryParams.workorderStateArr"
            placeholder="请选择工单状态"
            clearable
            multiple
          >
            <el-option
              v-for="dict in dict.type.workorder_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划开始时间" prop="planStartDate">
          <el-date-picker
            clearable
            v-model="queryParams.planStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="planEndDate">
          <el-date-picker
            clearable
            v-model="queryParams.planEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="	
交付日期"
          prop="woFinishDate"
        >
          <el-date-picker
            clearable
            v-model="queryParams.woFinishDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择	
交付日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="拆单原工单号" prop="parentNo">
          <el-input
            v-model="queryParams.parentNo"
            placeholder="请输入拆单原工单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <!-- <el-form-item label="工单描述" prop="woName">
          <el-input
            v-model="queryParams.woName"
            placeholder="请输入工单描述"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['mes:trcOrderWork:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['mes:trcOrderWork:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['mes:trcOrderWork:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['mes:trcOrderWork:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="trcOrderWorkList"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="工单id" align="center" prop="id" /> -->
        <el-table-column label="工单编号" align="center" prop="woNo" />

        <el-table-column label="工单类型" align="center" prop="woType" />
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="物料状态" align="center" prop="materialState" />
        <!-- <el-table-column label="产线id" align="center" prop="lineId" /> -->
        <el-table-column label="产线编码" align="center" prop="lineCode" />
        <el-table-column label="产线名称" align="center" prop="lineName" />
        <el-table-column
          label="排产数量"
          align="center"
          prop="productSchedule"
        />
        <el-table-column
          label="工单状态"
          align="center"
          prop="workorderState"
        />
        <el-table-column
          label="计划开始时间"
          align="center"
          prop="planStartDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planStartDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="计划结束时间"
          align="center"
          prop="planEndDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planEndDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="	
交付日期"
          align="center"
          prop="woFinishDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.woFinishDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="拆单原工单号" align="center" prop="parentNo" />
        <el-table-column label="工单描述" align="center" prop="woName" />
        <el-table-column label="创建人" align="center" prop="createBy" />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="修改人" align="center" prop="updateBy" />
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['mes:trcOrderWork:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['mes:trcOrderWork:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改工单管理对话框 -->
      <!-- <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="工单编号" prop="woNo" style="width: 240px">
            <el-input v-model="form.woNo" placeholder="请输入工单编号" />
          </el-form-item>
          <el-form-item
            label="物料编码"
            prop="materialCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialCode"
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item
            label="物料名称"
            prop="materialName"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialName"
              placeholder="请输入物料名称"
            />
          </el-form-item>
          <el-form-item
            label="物料状态"
            prop="materialState"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialState"
              placeholder="请输入物料状态"
            />
          </el-form-item>
          <el-form-item label="产线编码" prop="lineCode" style="width: 240px">
            <el-input v-model="form.lineCode" placeholder="请输入产线编码" />
          </el-form-item>
          <el-form-item label="产线名称" prop="lineName" style="width: 240px">
            <el-input v-model="form.lineName" placeholder="请输入产线名称" />
          </el-form-item>
          <el-form-item
            label="排产数量"
            prop="productSchedule"
            style="width: 240px"
          >
            <el-input
              v-model="form.productSchedule"
              placeholder="请输入排产数量"
            />
          </el-form-item>
          <el-form-item
            label="工单状态"
            prop="workorderState"
            style="width: 240px"
          >
            <el-select
              v-model="queryParams.workorderState"
              placeholder="请选择工单状态"
              clearable
            >
              <el-option
                v-for="dict in dict.type.workorder_state"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="计划开始时间"
            prop="planStartDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.planStartDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择计划开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="计划结束时间"
            prop="planEndDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.planEndDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择计划结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="	
交付日期"
            prop="woFinishDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.woFinishDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择	
交付日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="拆单原工单号"
            prop="parentNo"
            style="width: 240px"
          >
            <el-input
              v-model="form.parentNo"
              placeholder="请输入拆单原工单号"
            />
          </el-form-item>
          <el-form-item label="工单描述" prop="woName" style="width: 240px">
            <el-input v-model="form.woName" placeholder="请输入工单描述" />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer> -->

      <el-dialog
        title="新增订单详情"
        :visible.sync="open"
        width="50%"
        :before-close="handleClose"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item label="工单编号" prop="woNo">
                <el-input v-model="form.woNo" disabled placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工单类型" prop="workorderState">
                <el-select
                  v-model="form.workorderState"
                  placeholder="请选择工单类型"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.workorder_state"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
                <!-- <el-input
              v-model="form.workorderState"
              placeholder="请输入工单状态-字典workorder_state"
            /> -->
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="物料名称" prop="materialName">
                <el-input
                  v-model="form.materialName"
                  placeholder="请输入物料名称"
                />
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item label="物料编码" prop="materialCode">
                <el-input
                  v-model="form.materialCode"
                  placeholder="请选择物料编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工艺路径编码" prop="routeCode">
                <el-input
                  v-model="form.routeCode"
                  placeholder="请输入工艺路径编码"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item label="BOM编码" prop="bomCode">
                <el-input v-model="form.bomCode" placeholder="" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数量" prop="qty" style="width: 240px">
                <el-input v-model="form.qty" placeholder="请输入数量" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item
                label="计划开始时间"
                prop="planStartDate"
                style="width: 240px"
              >
                <el-date-picker
                  clearable
                  v-model="form.planStartDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择计划开始时间"
                  :picker-options="pickerStartTime"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="计划结束时间"
                prop="planEndDate"
                style="width: 240px"
              >
                <el-date-picker
                  clearable
                  v-model="form.planEndDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择计划结束时间"
                  :picker-options="pickerEndTime"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item
                label="交付日期"
                prop="woFinishDate"
                style="width: 240px"
              >
                <el-date-picker
                  clearable
                  v-model="form.woFinishDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择交付日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="woName" style="width: 240px">
                <el-input v-model="form.woName" placeholder="" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">返 回</el-button>
          <el-button type="primary" @click="submitForm">添加</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listTrcOrderWork,
  getTrcOrderWork,
  delTrcOrderWork,
  addTrcOrderWork,
  updateTrcOrderWork,
} from "@/api/mes/trcOrderWork";

export default {
  name: "TrcOrderWork",
  dicts: ["workorder_state"],
  data() {
    return {
      // 新增工单弹窗
      // addTrcOrderWorkVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单管理表格数据
      trcOrderWorkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        woNo: null,
        woType: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialState: null,
        lineId: null,
        lineCode: null,
        lineName: null,
        productSchedule: null,
        workorderState: null,
        planStartDate: null,
        planEndDate: null,
        woFinishDate: null,
        parentNo: null,
        woName: null,
        bomId: null,
        bomCode: null,
        bomName: null,
        routeId: null,
        routeCode: null,
        routeName: null,
        bomVersion: null,
        currentStateName: null,
        currentStateCode: null,
        previousStateName: null,
        previousStateCode: null,
        workorderStateArr: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  computed: {
    pickerStartTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.planStartDate) {
            let planStartDate = _this.form.planStartDate.replace(/-/g, "/");

            return time.getTime() >= new Date(planStartDate);
          }
        },
      };
    },

    pickerEndTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.planEndDate) {
            let planEndDate = _this.form.planEndDate.replace(/-/g, "/");

            return time.getTime() <= new Date(planEndDate);
          }
        },
      };
    },
  },
  methods: {
    /** 查询工单管理列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      listTrcOrderWork(this.queryParams).then((response) => {
        this.trcOrderWorkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        woNo: null,
        woType: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialState: null,
        lineId: null,
        lineCode: null,
        lineName: null,
        productSchedule: null,
        workorderState: null,
        planStartDate: null,
        planEndDate: null,
        woFinishDate: null,
        parentNo: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        woName: null,
        routeCode: null,
        bomCode: null,
        bomName: null,
        routeId: null,
        routeCode: null,
        routeName: null,
        bomVersion: null,
        currentStateName: null,
        currentStateCode: null,
        previousStateName: null,
        previousStateCode: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.importWlVisible = true;
      this.reset();
      this.open = true;
      this.title = "添加工单管理";
    },
    // 点击新增以后 又取消了的时候的方法
    handleClose(done) {
      this.reset();
      this.open = false;
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTrcOrderWork(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工单管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTrcOrderWork(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;

              this.resetQuery();
              this.getList();
            });
          } else {
            console.log(this.form);
            addTrcOrderWork(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.resetQuery();
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除工单管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delTrcOrderWork(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "mes/trcOrderWork/export",
        {
          ...this.queryParams,
        },
        `工单管理_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
